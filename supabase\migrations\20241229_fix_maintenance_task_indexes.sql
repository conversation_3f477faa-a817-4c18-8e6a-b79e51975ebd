-- =====================================================
-- FIX MAINTENANCE TASK INDEXES
-- =====================================================
-- This migration fixes the inconsistency where code references 'lich_bao_tri' table
-- but the actual table is 'cong_viec_bao_tri'.
-- We drop any existing 'lich_bao_tri' indexes and create proper indexes for 'cong_viec_bao_tri'.

-- =====================================================
-- 1. DROP OLD INDEXES FOR NON-EXISTENT lich_bao_tri TABLE
-- =====================================================

DROP INDEX IF EXISTS idx_lich_bao_tri_thiet_bi_id;
DROP INDEX IF EXISTS idx_lich_bao_tri_trang_thai;
DROP INDEX IF EXISTS idx_lich_bao_tri_loai_bao_tri;
DROP INDEX IF EXISTS idx_lich_bao_tri_ngay_bao_tri;
DROP INDEX IF EXISTS idx_lich_bao_tri_ngay_hoan_thanh;
DROP INDEX IF EXISTS idx_lich_bao_tri_nguoi_thuc_hien;
DROP INDEX IF EXISTS idx_lich_bao_tri_equipment_date;
DROP INDEX IF EXISTS idx_lich_bao_tri_status_date;
DROP INDEX IF EXISTS idx_lich_bao_tri_search_text;

-- =====================================================
-- 2. CREATE PROPER INDEXES FOR cong_viec_bao_tri TABLE
-- =====================================================

-- Equipment reference for JOINs
CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_thiet_bi_id
ON cong_viec_bao_tri (thiet_bi_id);

-- Status filtering (if column exists)
CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_trang_thai
ON cong_viec_bao_tri (trang_thai)
WHERE EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'cong_viec_bao_tri' AND column_name = 'trang_thai'
);

-- Maintenance type filtering (if column exists)
CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_loai_bao_tri
ON cong_viec_bao_tri (loai_bao_tri)
WHERE EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'cong_viec_bao_tri' AND column_name = 'loai_bao_tri'
);

-- Plan reference for filtering by maintenance plan
CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_ke_hoach_id
ON cong_viec_bao_tri (ke_hoach_bao_tri_id);

-- Month-based completion tracking indexes
CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_thang_1
ON cong_viec_bao_tri (thang_1_hoan_thanh);

CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_thang_2
ON cong_viec_bao_tri (thang_2_hoan_thanh);

CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_thang_3
ON cong_viec_bao_tri (thang_3_hoan_thanh);

CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_thang_6
ON cong_viec_bao_tri (thang_6_hoan_thanh);

CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_thang_12
ON cong_viec_bao_tri (thang_12_hoan_thanh);

-- Composite index for plan + month queries (most common pattern)
CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_plan_month1
ON cong_viec_bao_tri (ke_hoach_bao_tri_id, thang_1_hoan_thanh);

CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_plan_month6
ON cong_viec_bao_tri (ke_hoach_bao_tri_id, thang_6_hoan_thanh);

-- Text search on description (if column exists)
CREATE INDEX IF NOT EXISTS idx_cong_viec_bao_tri_search_text
ON cong_viec_bao_tri USING gin (
  (mo_ta || ' ' || COALESCE(ghi_chu, '')) gin_trgm_ops
)
WHERE EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'cong_viec_bao_tri' AND column_name = 'mo_ta'
);

-- =====================================================
-- 3. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON INDEX idx_cong_viec_bao_tri_thiet_bi_id IS 'Equipment reference for maintenance tasks';
COMMENT ON INDEX idx_cong_viec_bao_tri_ke_hoach_id IS 'Maintenance plan reference for task filtering';
COMMENT ON INDEX idx_cong_viec_bao_tri_plan_month1 IS 'Composite index for plan and month 1 completion queries';
COMMENT ON INDEX idx_cong_viec_bao_tri_plan_month6 IS 'Composite index for plan and month 6 completion queries';

-- =====================================================
-- 4. VERIFICATION QUERIES
-- =====================================================

-- Check if the table exists and show its structure
DO $$
DECLARE
    table_exists BOOLEAN;
    column_count INTEGER;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'cong_viec_bao_tri'
    ) INTO table_exists;
    
    IF table_exists THEN
        SELECT COUNT(*) INTO column_count
        FROM information_schema.columns 
        WHERE table_name = 'cong_viec_bao_tri';
        
        RAISE NOTICE 'Table cong_viec_bao_tri exists with % columns', column_count;
    ELSE
        RAISE WARNING 'Table cong_viec_bao_tri does not exist!';
    END IF;
END $$;

-- Show columns of cong_viec_bao_tri table
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'cong_viec_bao_tri'
ORDER BY ordinal_position; 