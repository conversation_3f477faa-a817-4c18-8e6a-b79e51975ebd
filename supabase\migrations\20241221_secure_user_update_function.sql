-- =====================================================
-- SECURE USER FUNCTIONS WITH PASSWORD HASHING
-- =====================================================
-- Functions to securely create and update users with password hashing

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Add hashed password column if it doesn't exist
ALTER TABLE nhan_vien ADD COLUMN IF NOT EXISTS hashed_password TEXT;

-- Simple username validation function (no restrictions)
CREATE OR REPLACE FUNCTION validate_username(username TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  -- Only check that username is not empty and doesn't contain spaces
  RETURN username IS NOT NULL
    AND length(trim(username)) > 0
    AND username !~ '\s';
END;
$$;

-- Function to create new user with hashed password
CREATE OR REPLACE FUNCTION create_user(
  p_username TEXT,
  p_password TEXT,
  p_full_name TEXT,
  p_role TEXT,
  p_khoa_phong TEXT DEFAULT NULL
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id INTEGER;
BEGIN
  -- Validate inputs
  IF NOT validate_username(p_username) THEN
    RAISE EXCEPTION 'Invalid username format';
  END IF;

  -- No password validation - allow any password

  -- Insert new user with hashed password
  INSERT INTO nhan_vien (username, password, hashed_password, full_name, role, khoa_phong)
  VALUES (p_username, 'hashed password', crypt(p_password, gen_salt('bf', 12)), p_full_name, p_role, p_khoa_phong)
  RETURNING id INTO new_user_id;

  RETURN new_user_id;
END;
$$;

-- Function to update user information (admin only)
CREATE OR REPLACE FUNCTION update_user_info(
  p_admin_user_id INTEGER,
  p_target_user_id INTEGER,
  p_username TEXT,
  p_password TEXT,
  p_full_name TEXT,
  p_role TEXT,
  p_khoa_phong TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  admin_role TEXT;
  current_username TEXT;
BEGIN
  -- Verify admin permissions
  SELECT role INTO admin_role
  FROM nhan_vien
  WHERE id = p_admin_user_id;
  
  IF admin_role != 'admin' THEN
    RAISE EXCEPTION 'Access denied: Admin privileges required';
  END IF;
  
  -- Validate inputs
  IF NOT validate_username(p_username) THEN
    RAISE EXCEPTION 'Invalid username format';
  END IF;
  
  -- Check if target user exists
  SELECT username INTO current_username
  FROM nhan_vien
  WHERE id = p_target_user_id;
  
  IF current_username IS NULL THEN
    RAISE EXCEPTION 'User not found with ID: %', p_target_user_id;
  END IF;
  
  -- Update user information with hashed password
  UPDATE nhan_vien
  SET username = p_username,
      hashed_password = crypt(p_password, gen_salt('bf', 12)),
      password = 'hashed password', -- Set placeholder text
      full_name = p_full_name,
      role = p_role,
      khoa_phong = p_khoa_phong
  WHERE id = p_target_user_id;
  
  -- Invalidate all existing sessions for this user if password changed
  UPDATE user_sessions
  SET is_active = FALSE
  WHERE user_id = p_target_user_id;
  
  -- Log the action
  INSERT INTO audit_log (
    user_id, action, resource_type, resource_id, 
    new_values, ip_address
  ) VALUES (
    p_admin_user_id, 'USER_UPDATE', 'nhan_vien', p_target_user_id,
    jsonb_build_object(
      'username', p_username, 
      'full_name', p_full_name, 
      'role', p_role, 
      'khoa_phong', p_khoa_phong,
      'password_updated', true
    ),
    inet_client_addr()
  );
  
  RETURN TRUE;
END;
$$;

-- Function to change password
CREATE OR REPLACE FUNCTION change_password(
  p_user_id INTEGER,
  p_old_password TEXT,
  p_new_password TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_hash TEXT;
  current_plain TEXT;
BEGIN
  -- Get current password info
  SELECT hashed_password, password INTO current_hash, current_plain
  FROM nhan_vien
  WHERE id = p_user_id;

  -- Verify old password (try hashed first, then plain text for backward compatibility)
  IF current_hash IS NOT NULL AND current_hash = crypt(p_old_password, current_hash) THEN
    -- Hashed password verification successful
  ELSIF current_plain IS NOT NULL AND current_plain = p_old_password THEN
    -- Plain text password verification successful (backward compatibility)
  ELSE
    RETURN FALSE; -- Invalid old password
  END IF;

  -- Update password with hash
  UPDATE nhan_vien
  SET hashed_password = crypt(p_new_password, gen_salt('bf', 12)),
      password = 'hashed password'
  WHERE id = p_user_id;

  RETURN TRUE;
END;
$$;

-- Grant permissions for the new functions
GRANT EXECUTE ON FUNCTION validate_username(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_user(TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_info(INTEGER, INTEGER, TEXT, TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION change_password(INTEGER, TEXT, TEXT) TO authenticated;

-- Add comments
COMMENT ON FUNCTION validate_username(TEXT) IS 'Simple username validation (no spaces, not empty)';
COMMENT ON FUNCTION create_user(TEXT, TEXT, TEXT, TEXT, TEXT) IS 'Create new user with bcrypt hashed password';
COMMENT ON FUNCTION update_user_info(INTEGER, INTEGER, TEXT, TEXT, TEXT, TEXT, TEXT) IS 'Securely update user information with password hashing (admin only)';
COMMENT ON FUNCTION change_password(INTEGER, TEXT, TEXT) IS 'Change user password with verification and hashing';
