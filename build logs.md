2025-07-07T01:02:45.738Z	Initializing build environment...
2025-07-07T01:02:52.467Z	Success: Finished initializing build environment
2025-07-07T01:02:53.324Z	Cloning repository...
2025-07-07T01:02:54.914Z	Detected the following tools from environment: npm@10.9.2, nodejs@22.16.0
2025-07-07T01:02:54.916Z	Restoring from dependencies cache
2025-07-07T01:02:54.918Z	Restoring from build output cache
2025-07-07T01:02:55.637Z	Installing project dependencies: npm clean-install --progress=false
2025-07-07T01:03:01.016Z	npm warn deprecated sourcemap-codec@1.4.8: Please use @jridgewell/sourcemap-codec instead
2025-07-07T01:03:01.176Z	npm warn deprecated rollup-plugin-terser@7.0.2: This package has been deprecated and is no longer maintained. Please use @rollup/plugin-terser
2025-07-07T01:03:01.218Z	npm warn deprecated rimraf@2.7.1: Rimraf versions prior to v4 are no longer supported
2025-07-07T01:03:02.216Z	npm warn deprecated workbox-google-analytics@6.6.0: It is not compatible with newer versions of GA starting with v4, as long as you are using GAv3 it should be ok, but the package is not longer being maintained
2025-07-07T01:03:02.354Z	npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
2025-07-07T01:03:02.795Z	npm warn deprecated workbox-cacheable-response@6.6.0: workbox-background-sync@6.6.0
2025-07-07T01:03:06.425Z	npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
2025-07-07T01:03:06.478Z	npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
2025-07-07T01:03:06.501Z	npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
2025-07-07T01:03:19.613Z	
2025-07-07T01:03:19.613Z	added 719 packages, and audited 720 packages in 24s
2025-07-07T01:03:19.616Z	
2025-07-07T01:03:19.616Z	148 packages are looking for funding
2025-07-07T01:03:19.617Z	  run `npm fund` for details
2025-07-07T01:03:19.619Z	
2025-07-07T01:03:19.619Z	3 vulnerabilities (1 low, 1 moderate, 1 high)
2025-07-07T01:03:19.619Z	
2025-07-07T01:03:19.620Z	To address issues that do not require attention, run:
2025-07-07T01:03:19.620Z	  npm audit fix
2025-07-07T01:03:19.620Z	
2025-07-07T01:03:19.620Z	Some issues need review, and may require choosing
2025-07-07T01:03:19.620Z	a different dependency.
2025-07-07T01:03:19.621Z	
2025-07-07T01:03:19.621Z	Run `npm audit` for details.
2025-07-07T01:03:19.717Z	Executing user build command: npm run build
2025-07-07T01:03:19.968Z	
2025-07-07T01:03:19.968Z	> nextn@0.1.0 build
2025-07-07T01:03:19.968Z	> next build
2025-07-07T01:03:19.968Z	
2025-07-07T01:03:27.136Z	⚠ No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache
2025-07-07T01:03:27.161Z	Attention: Next.js now collects completely anonymous telemetry regarding usage.
2025-07-07T01:03:27.161Z	This information is used to shape Next.js' roadmap and prioritize features.
2025-07-07T01:03:27.161Z	You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
2025-07-07T01:03:27.162Z	https://nextjs.org/telemetry
2025-07-07T01:03:27.162Z	
2025-07-07T01:03:27.329Z	   ▲ Next.js 15.3.3
2025-07-07T01:03:27.330Z	
2025-07-07T01:03:27.350Z	   Creating an optimized production build ...
2025-07-07T01:03:27.787Z	> [PWA] Compile server
2025-07-07T01:03:27.788Z	> [PWA] Compile server
2025-07-07T01:03:27.789Z	> [PWA] Compile client (static)
2025-07-07T01:03:27.789Z	> [PWA] Auto register service worker with: /opt/buildhome/repo/node_modules/next-pwa/register.js
2025-07-07T01:03:27.789Z	> [PWA] Service worker: /opt/buildhome/repo/public/sw.js
2025-07-07T01:03:27.789Z	> [PWA]   url: /sw.js
2025-07-07T01:03:27.790Z	> [PWA]   scope: /
2025-07-07T01:04:01.754Z	 ⚠ Compiled with warnings in 33.0s
2025-07-07T01:04:01.755Z	
2025-07-07T01:04:01.755Z	./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
2025-07-07T01:04:01.755Z	Critical dependency: the request of a dependency is an expression
2025-07-07T01:04:01.756Z	
2025-07-07T01:04:01.756Z	Import trace for requested module:
2025-07-07T01:04:01.756Z	./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
2025-07-07T01:04:01.756Z	./node_modules/@supabase/realtime-js/dist/main/index.js
2025-07-07T01:04:01.756Z	./node_modules/@supabase/supabase-js/dist/module/index.js
2025-07-07T01:04:01.756Z	./src/lib/supabase.ts
2025-07-07T01:04:01.757Z	./src/app/(app)/dashboard/page.tsx
2025-07-07T01:04:01.757Z	
2025-07-07T01:04:01.766Z	   Skipping validation of types
2025-07-07T01:04:01.767Z	   Skipping linting
2025-07-07T01:04:02.221Z	   Collecting page data ...
2025-07-07T01:04:06.028Z	   Generating static pages (0/14) ...
2025-07-07T01:04:07.109Z	   Generating static pages (3/14) 
2025-07-07T01:04:07.109Z	   Generating static pages (6/14) 
2025-07-07T01:04:07.252Z	   Generating static pages (10/14) 
2025-07-07T01:04:07.253Z	 ✓ Generating static pages (14/14)
2025-07-07T01:04:07.635Z	   Finalizing page optimization ...
2025-07-07T01:04:07.639Z	   Collecting build traces ...
2025-07-07T01:04:17.405Z	
2025-07-07T01:04:17.409Z	Route (app)                                 Size  First Load JS
2025-07-07T01:04:17.410Z	┌ ○ /                                    2.17 kB         150 kB
2025-07-07T01:04:17.410Z	├ ○ /_not-found                            141 B         102 kB
2025-07-07T01:04:17.410Z	├ ○ /dashboard                           10.8 kB         207 kB
2025-07-07T01:04:17.410Z	├ ○ /equipment                           32.7 kB         285 kB
2025-07-07T01:04:17.410Z	├ ○ /maintenance                         21.9 kB         260 kB
2025-07-07T01:04:17.410Z	├ ○ /qr-scanner                          6.09 kB         120 kB
2025-07-07T01:04:17.411Z	├ ○ /repair-requests                     29.2 kB         240 kB
2025-07-07T01:04:17.411Z	├ ○ /reports                             2.73 kB         117 kB
2025-07-07T01:04:17.411Z	├ ○ /test-auth                           1.46 kB         140 kB
2025-07-07T01:04:17.411Z	├ ○ /transfers                           20.2 kB         205 kB
2025-07-07T01:04:17.411Z	└ ○ /users                               9.24 kB         220 kB
2025-07-07T01:04:17.411Z	+ First Load JS shared by all             102 kB
2025-07-07T01:04:17.411Z	  ├ chunks/1684-c85b88aa75dff178.js      46.4 kB
2025-07-07T01:04:17.411Z	  ├ chunks/4bd1b696-3a8075e66968045e.js  53.2 kB
2025-07-07T01:04:17.411Z	  └ other shared chunks (total)          2.56 kB
2025-07-07T01:04:17.412Z	
2025-07-07T01:04:17.412Z	
2025-07-07T01:04:17.412Z	○  (Static)  prerendered as static content
2025-07-07T01:04:17.412Z	
2025-07-07T01:04:17.565Z	Success: Build command completed
2025-07-07T01:04:17.566Z	Executing user deploy command: npx wrangler deploy
2025-07-07T01:04:33.526Z	npm warn exec The following package was not found and will be installed: wrangler@4.23.0
2025-07-07T01:05:22.996Z	
2025-07-07T01:05:22.997Z	 ⛅️ wrangler 4.23.0
2025-07-07T01:05:22.997Z	───────────────────
2025-07-07T01:05:23.003Z	
2025-07-07T01:05:23.076Z	✘ [ERROR] Missing entry-point to Worker script or to assets directory
2025-07-07T01:05:23.076Z	
2025-07-07T01:05:23.076Z	  
2025-07-07T01:05:23.076Z	  If there is code to deploy, you can either:
2025-07-07T01:05:23.076Z	  - Specify an entry-point to your Worker script via the command line (ex: `npx wrangler deploy src/index.ts`)
2025-07-07T01:05:23.076Z	  - Or create a "wrangler.jsonc" file containing:
2025-07-07T01:05:23.077Z	  
2025-07-07T01:05:23.077Z	  ```
2025-07-07T01:05:23.077Z	  {
2025-07-07T01:05:23.077Z	    "name": "worker-name",
2025-07-07T01:05:23.077Z	    "compatibility_date": "2025-07-07",
2025-07-07T01:05:23.077Z	    "main": "src/index.ts"
2025-07-07T01:05:23.077Z	  }
2025-07-07T01:05:23.077Z	  ```
2025-07-07T01:05:23.077Z	  
2025-07-07T01:05:23.077Z	  
2025-07-07T01:05:23.078Z	  If are uploading a directory of assets, you can either:
2025-07-07T01:05:23.078Z	  - Specify the path to the directory of assets via the command line: (ex: `npx wrangler deploy --assets=./dist`)
2025-07-07T01:05:23.078Z	  - Or create a "wrangler.jsonc" file containing:
2025-07-07T01:05:23.078Z	  
2025-07-07T01:05:23.078Z	  ```
2025-07-07T01:05:23.078Z	  {
2025-07-07T01:05:23.078Z	    "name": "worker-name",
2025-07-07T01:05:23.079Z	    "compatibility_date": "2025-07-07",
2025-07-07T01:05:23.079Z	    "assets": {
2025-07-07T01:05:23.079Z	      "directory": "./dist"
2025-07-07T01:05:23.079Z	    }
2025-07-07T01:05:23.079Z	  }
2025-07-07T01:05:23.079Z	  ```
2025-07-07T01:05:23.079Z	  
2025-07-07T01:05:23.079Z	
2025-07-07T01:05:23.079Z	
2025-07-07T01:05:23.080Z	
2025-07-07T01:05:23.080Z	Cloudflare collects anonymous telemetry about your usage of Wrangler. Learn more at https://github.com/cloudflare/workers-sdk/tree/main/packages/wrangler/telemetry.md
2025-07-07T01:05:23.094Z	🪵  Logs were written to "/opt/buildhome/.config/.wrangler/logs/wrangler-2025-07-07_01-05-12_656.log"
2025-07-07T01:05:23.217Z	Failed: error occurred while running deploy command