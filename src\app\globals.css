@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 196 55% 92%;
    --foreground: 224 71.4% 4.1%;
    --card: 196 55% 96%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 196 55% 96%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 194 38% 43%;
    --primary-foreground: 355.7 100% 97.3%;
    --primary-50: 194 38% 95%;
    --primary-100: 194 38% 85%;
    --primary-500: 194 38% 50%;
    --primary-600: 194 38% 43%;
    --primary-700: 194 38% 35%;
    --secondary: 30 90% 88%;
    --secondary-foreground: 25 80% 25%;
    --secondary-50: 30 90% 95%;
    --secondary-600: 30 90% 70%;
    --secondary-700: 30 90% 60%;
    --muted: 195 45% 89%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 190 39% 52%;
    --accent-foreground: 355.7 100% 97.3%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 195 40% 85%;
    --input: 195 40% 88%;
    --ring: 194 38% 43%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --color-planned: hsl(var(--chart-3));
    --color-actual: hsl(var(--chart-2));
    --radius: 0.5rem;
    --sidebar-background: 196 55% 98%;
    --sidebar-foreground: 224 71.4% 4.1%;
    --sidebar-primary: 194 38% 43%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 195 45% 92%;
    --sidebar-accent-foreground: 194 38% 43%;
    --sidebar-border: 195 40% 85%;
    --sidebar-ring: 194 38% 43%;
  }
  .dark {
    --background: 200 40% 10%;
    --foreground: 0 0% 98%;
    --card: 200 40% 12%;
    --card-foreground: 0 0% 98%;
    --popover: 200 40% 10%;
    --popover-foreground: 0 0% 98%;
    --primary: 194 38% 53%;
    --primary-foreground: 194 38% 10%;
    --primary-50: 194 38% 15%;
    --primary-100: 194 38% 25%;
    --primary-500: 194 38% 50%;
    --primary-600: 194 38% 53%;
    --primary-700: 194 38% 65%;
    --secondary: 30 70% 20%;
    --secondary-foreground: 30 90% 85%;
    --secondary-50: 30 70% 15%;
    --secondary-600: 30 70% 30%;
    --secondary-700: 30 70% 40%;
    --muted: 200 30% 20%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 190 39% 62%;
    --accent-foreground: 190 39% 10%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 200 30% 20%;
    --input: 200 30% 20%;
    --ring: 194 38% 53%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --color-planned: hsl(var(--chart-3));
    --color-actual: hsl(var(--chart-2));
    --sidebar-background: 200 40% 8%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 194 38% 53%;
    --sidebar-primary-foreground: 194 38% 10%;
    --sidebar-accent: 200 30% 15%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 200 30% 20%;
    --sidebar-ring: 194 38% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Touch Target Optimization - Mobile UX Standards */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-target-sm {
    @apply min-h-[40px] min-w-[40px];
  }

  .touch-target-lg {
    @apply min-h-[48px] min-w-[48px];
  }

  /* Mobile-optimized button spacing */
  .mobile-button-spacing {
    @apply gap-3 md:gap-2;
  }

  /* Enhanced touch areas for small elements */
  .touch-area-enhanced {
    @apply relative;
  }

  .touch-area-enhanced::before {
    content: '';
    @apply absolute inset-0 min-h-[44px] min-w-[44px];
    /* Center the touch area */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  /* Mobile-friendly padding for interactive elements */
  .mobile-interactive {
    @apply px-4 py-3 md:px-3 md:py-2;
  }

  /* Improved spacing for mobile cards */
  .mobile-card-spacing {
    @apply p-4 gap-3 md:p-3 md:gap-2;
  }

  /* Responsive Typography Scaling */
  .text-responsive-xs {
    font-size: clamp(0.75rem, 2vw, 0.875rem);
    line-height: clamp(1.4, 1.5, 1.6);
  }

  .text-responsive-sm {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    line-height: clamp(1.4, 1.5, 1.6);
  }

  .text-responsive-base {
    font-size: clamp(1rem, 3vw, 1.125rem);
    line-height: clamp(1.5, 1.6, 1.7);
  }

  .text-responsive-lg {
    font-size: clamp(1.125rem, 3.5vw, 1.25rem);
    line-height: clamp(1.4, 1.5, 1.6);
  }

  .text-responsive-xl {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    line-height: clamp(1.3, 1.4, 1.5);
  }

  .text-responsive-2xl {
    font-size: clamp(1.5rem, 5vw, 2rem);
    line-height: clamp(1.2, 1.3, 1.4);
  }

  .text-responsive-3xl {
    font-size: clamp(1.875rem, 6vw, 2.5rem);
    line-height: clamp(1.1, 1.2, 1.3);
  }

  /* Responsive headings with proper hierarchy */
  .heading-responsive-h1 {
    font-size: clamp(1.875rem, 6vw, 2.5rem);
    line-height: clamp(1.1, 1.2, 1.3);
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .heading-responsive-h2 {
    font-size: clamp(1.5rem, 5vw, 2rem);
    line-height: clamp(1.2, 1.3, 1.4);
    font-weight: 600;
    letter-spacing: -0.025em;
  }

  .heading-responsive-h3 {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    line-height: clamp(1.3, 1.4, 1.5);
    font-weight: 600;
  }

  .heading-responsive-h4 {
    font-size: clamp(1.125rem, 3.5vw, 1.25rem);
    line-height: clamp(1.4, 1.5, 1.6);
    font-weight: 500;
  }

  /* Body text variants */
  .body-responsive {
    font-size: clamp(1rem, 3vw, 1.125rem);
    line-height: clamp(1.5, 1.6, 1.7);
  }

  .body-responsive-sm {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    line-height: clamp(1.4, 1.5, 1.6);
  }

  /* Caption and helper text */
  .caption-responsive {
    font-size: clamp(0.75rem, 2vw, 0.875rem);
    line-height: clamp(1.4, 1.5, 1.6);
    color: hsl(var(--muted-foreground));
  }

  /* Button text optimization */
  .button-text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    font-weight: 500;
    letter-spacing: 0.025em;
  }

  /* Progressive disclosure animations */
  .collapsible-enter {
    animation: slideDown 0.3s ease-out;
  }

  .collapsible-exit {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
    to {
      opacity: 1;
      transform: translateY(0);
      max-height: 1000px;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 1;
      transform: translateY(0);
      max-height: 1000px;
    }
    to {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
  }

  /* Smooth toggle button transitions */
  .toggle-button {
    transition: all 0.2s ease-in-out;
  }

  .toggle-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}
