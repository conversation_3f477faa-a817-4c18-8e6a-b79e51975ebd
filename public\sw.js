if(!self.define){let s,e={};const n=(n,t)=>(n=new URL(n+".js",t).href,e[n]||new Promise(e=>{if("document"in self){const s=document.createElement("script");s.src=n,s.onload=e,document.head.appendChild(s)}else s=n,importScripts(n),e()}).then(()=>{let s=e[n];if(!s)throw new Error(`Module ${n} didn’t register its module`);return s}));self.define=(t,i)=>{const a=s||("document"in self?document.currentScript.src:"")||location.href;if(e[a])return;let c={};const r=s=>n(s,a),d={module:{uri:a},exports:c,require:r};e[a]=Promise.all(t.map(s=>d[s]||r(s))).then(s=>(i(...s),c))}}define(["./workbox-4754cb34"],function(s){"use strict";importScripts(),self.skipWaiting(),s.clientsClaim(),s.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"b11503cbeafbfd73e0b9335644d23c27"},{url:"/_next/static/DsRO4IMK6Iv1dzmN9bSIw/_buildManifest.js",revision:"133311efefbbfcabbc8c80b50a1344a4"},{url:"/_next/static/DsRO4IMK6Iv1dzmN9bSIw/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/1064-99ced236495f1d32.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/1342-db6f506c5419ded7.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/1348-c49a54fece5e8558.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/1367.bb2df06dc667d61d.js",revision:"bb2df06dc667d61d"},{url:"/_next/static/chunks/1413-c159b17a98084fee.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/1481-274f4a1f10ddfdbe.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/1684-2e8a14b0e33c9ab9.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/1861-fd2fe0e8fd35fc22.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/1921.86a9d9abb90b313a.js",revision:"86a9d9abb90b313a"},{url:"/_next/static/chunks/2170a4aa.01b7318bdd55379d.js",revision:"01b7318bdd55379d"},{url:"/_next/static/chunks/2848-f77454f6806a64e9.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/2960-17841c59874f2bbe.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/3127-5ae1e84114de87c5.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/3539-3002121b0b075ea1.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/3617-59ff8789197f9f38.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/3664-b67f2fd0d14f9584.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/4052-80368a87abca4cd3.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/4684.e742382fff3366c3.js",revision:"e742382fff3366c3"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/4920-09438c9972cd7127.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/4982-dbc8e50c3d10c00b.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/4bd1b696-433475f3472104df.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/5152-351c313e21b729c7.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/5304.2a273b66a53cf873.js",revision:"2a273b66a53cf873"},{url:"/_next/static/chunks/5508.4b534adbdb889dff.js",revision:"4b534adbdb889dff"},{url:"/_next/static/chunks/565-f24d6a0219cf9206.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/5973-6abd49b6b36837d4.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/6534.7ddd7f6e70794ad2.js",revision:"7ddd7f6e70794ad2"},{url:"/_next/static/chunks/6874-240e4016d857918e.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/6967-f43c3d8629f3196e.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/699-4c48fe085addcd53.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/7655-369723c1a470d0dc.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/769.3da2bcf8764ae552.js",revision:"3da2bcf8764ae552"},{url:"/_next/static/chunks/7886-f0a5c12a0752cb7a.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/8250-e5bb44473aeae522.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/8436.cab94b59cca0a8ff.js",revision:"cab94b59cca0a8ff"},{url:"/_next/static/chunks/870.2fe92867fd1b6f33.js",revision:"2fe92867fd1b6f33"},{url:"/_next/static/chunks/8894.cc245b4c85bb3e0b.js",revision:"cc245b4c85bb3e0b"},{url:"/_next/static/chunks/9190-563d403ce5c107dc.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/9341.a5e04b1003bfe050.js",revision:"a5e04b1003bfe050"},{url:"/_next/static/chunks/9354-0e94e08f451c9ddd.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/9413-10bcd622ad766098.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/9585.e8a13b33bd3ad93e.js",revision:"e8a13b33bd3ad93e"},{url:"/_next/static/chunks/9748-88df0698c2137452.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/9861.a8374c6efda1123f.js",revision:"a8374c6efda1123f"},{url:"/_next/static/chunks/app/(app)/dashboard/page-e197d0499a5dc6e1.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/(app)/equipment/page-b03b2184ae629b64.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/(app)/layout-bdf668488bfe2737.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/(app)/maintenance/page-cbc65dc4f5f1802d.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/(app)/qr-scanner/page-5e9840c1fd7d1242.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/(app)/repair-requests/page-bcc99b8732f61bca.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/(app)/reports/page-8d2a4e18a3a3e6b5.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/(app)/transfers/page-680e4ba4b3aa1819.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/(app)/users/page-ba1b05f18b51774e.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/_not-found/page-96a7a8162a2b0e3b.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/layout-b7e8147a06fb2419.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/not-found-a1f8e156c24b57fe.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/page-6567426dbe5b6605.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/app/test-auth/page-d4cd27aa4336e49c.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/d0f5a89a.68ddfd0fe63b98b9.js",revision:"68ddfd0fe63b98b9"},{url:"/_next/static/chunks/framework-2c2be674e67eda3d.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/main-app-2a87d62be058a59c.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/main-f0c49f4ebb047f5e.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/pages/_app-a61587d9d4172ff4.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/pages/_error-85dc5b0cc18f4481.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-25ff8acd36921b5c.js",revision:"DsRO4IMK6Iv1dzmN9bSIw"},{url:"/_next/static/css/9e411f96721b0ee5.css",revision:"9e411f96721b0ee5"},{url:"/firebase-messaging-sw.js",revision:"5608085f198815c08e341105c9a83617"},{url:"/icons/icon-192x192.png",revision:"4139946c7843179db799270dbb153298"},{url:"/icons/icon-512x512.png",revision:"1d782a98e4fa3291375722ab8c34ae7e"},{url:"/icons/icon-maskable-192x192.png",revision:"f39e1d51e59e07fc9c08100928903e55"},{url:"/icons/icon-maskable-512x512.png",revision:"2f25391928588879d93e452ca6ab0563"},{url:"/manifest.json",revision:"bf83d1aa4f044fc4308ef2b84977c6b7"},{url:"/screenshots/placeholder-desktop.png",revision:"68e709f9a056166bfd332c30135eb9c5"},{url:"/screenshots/placeholder-mobile.png",revision:"eef1e61d9677052619769ec64f38f643"}],{ignoreURLParametersMatching:[]}),s.cleanupOutdatedCaches(),s.registerRoute("/",new s.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:s,response:e,event:n,state:t})=>e&&"opaqueredirect"===e.type?new Response(e.body,{status:200,statusText:"OK",headers:e.headers}):e}]}),"GET"),s.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new s.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),s.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new s.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),s.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new s.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new s.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),s.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new s.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new s.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\/_next\/image\?url=.+$/i,new s.StaleWhileRevalidate({cacheName:"next-image",plugins:[new s.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:mp3|wav|ogg)$/i,new s.CacheFirst({cacheName:"static-audio-assets",plugins:[new s.RangeRequestsPlugin,new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:mp4)$/i,new s.CacheFirst({cacheName:"static-video-assets",plugins:[new s.RangeRequestsPlugin,new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:js)$/i,new s.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:css|less)$/i,new s.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new s.StaleWhileRevalidate({cacheName:"next-data",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(/\.(?:json|xml|csv)$/i,new s.NetworkFirst({cacheName:"static-data-assets",plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>{if(!(self.origin===s.origin))return!1;const e=s.pathname;return!e.startsWith("/api/auth/")&&!!e.startsWith("/api/")},new s.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>{if(!(self.origin===s.origin))return!1;return!s.pathname.startsWith("/api/")},new s.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),s.registerRoute(({url:s})=>!(self.origin===s.origin),new s.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new s.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
